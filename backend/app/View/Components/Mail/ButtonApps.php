<?php

declare(strict_types=1);

namespace App\View\Components\Mail;

use Illuminate\View\Component;

class ButtonApps extends Component
{
    public $apple;

    public $android;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(array $apple, array $android)
    {
        $this->apple = $apple;
        $this->android = $android;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('vendor.mail.template-curycliente.buttonApps');
    }
}
